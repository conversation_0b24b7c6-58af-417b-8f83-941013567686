import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Target, Eye, Award, Users } from 'lucide-react';
import OptimizedImage from '../components/OptimizedImage';
import { BoardMembersCarousel } from '../components/BoardMembersCarousel';

const About = () => {
  const leadership = [
    {
      name: "<PERSON> <PERSON><PERSON><PERSON>",
      title: "Chairman",
      image: "/assets/images/members/MAJ MD TOUFIKUL MUNEEM.jpg",
      bio: "Major <PERSON><PERSON><PERSON><PERSON> brings distinguished military leadership and strategic vision to Soldiers Builders. His commitment to excellence and precision drives the company's mission to create exceptional residential developments.",
      experience: "With extensive experience in military operations and strategic planning, Major <PERSON><PERSON> has successfully led complex projects requiring meticulous attention to detail and unwavering commitment to quality.",
      expertise: ["Strategic Leadership", "Project Management", "Military Precision", "Quality Assurance"]
    },
    {
      name: "Brig Gen <PERSON>",
      title: "Managing Director",
      image: "/assets/images/members/BRIG GEN MD ZOBAIDUR RAHMAN.jpg",
      bio: "Brigadier General <PERSON><PERSON> serves as the Managing Director, bringing decades of military leadership and operational excellence to guide Soldiers Builders' day-to-day operations and strategic initiatives.",
      experience: "His distinguished military career has equipped him with exceptional leadership skills, strategic thinking, and the ability to execute complex projects with precision and efficiency.",
      expertise: ["Operations Management", "Strategic Planning", "Leadership Development", "Risk Management"]
    },
    {
      name: "Maj Gen Kamrul H Khan",
      title: "Board Member",
      image: "/assets/images/members/MG KAMRUL H KHAN.jpg",
      bio: "Major General Kamrul H Khan brings extensive military leadership and strategic expertise to the board, with a distinguished career in defense operations and organizational management.",
      experience: "His military background provides invaluable insights into project management, team coordination, and maintaining the highest standards of operational excellence.",
      expertise: ["Military Leadership", "Strategic Operations", "Team Management", "Quality Control"]
    },
    {
      name: "Shanara Begum",
      title: "Board Member",
      image: "/assets/images/members/SHANARA BEGUM.jpg",
      bio: "Shanara Begum contributes valuable expertise in governance and strategic planning, ensuring that Soldiers Builders maintains its commitment to excellence and ethical business practices.",
      experience: "With extensive experience in organizational leadership and strategic decision-making, she plays a crucial role in guiding the company's long-term vision and growth.",
      expertise: ["Corporate Governance", "Strategic Planning", "Business Ethics", "Organizational Development"]
    },
    {
      name: "Brig Gen Md Habibur Rahman",
      title: "Board Member",
      image: "/assets/images/members/BG MD HABIBUR RAHMAN.jpg",
      bio: "Brigadier General Md Habibur Rahman brings decades of military experience and leadership excellence to guide strategic initiatives and operational efficiency at Soldiers Builders.",
      experience: "His distinguished military career has equipped him with exceptional skills in project execution, risk management, and maintaining the highest standards of quality and discipline.",
      expertise: ["Project Execution", "Risk Assessment", "Quality Assurance", "Operational Excellence"]
    },
    {
      name: "Dr Istiaque Anwar",
      title: "Board Member",
      image: "/assets/images/members/DR ISTIAQUE ANWAR.jpg",
      bio: "Dr. Istiaque Anwar brings academic excellence and research expertise to the board, contributing valuable insights into innovative construction methodologies and sustainable development practices.",
      experience: "His academic background and research experience provide the company with cutting-edge knowledge in construction technology and sustainable building practices.",
      expertise: ["Construction Technology", "Sustainable Development", "Research & Innovation", "Academic Excellence"]
    },
    {
      name: "Prof Dr A Shakur Khan",
      title: "Board Member",
      image: "/assets/images/members/PROF A SAKUR KHAN.jpg"
    },
    {
      name: "Lt Col Md Emdadul Haque",
      title: "Board Member",
      image: "/assets/images/members/LT COL MD EMDADUL HAQUE.jpg"
    },
    {
      name: "Prof Dr Begum Nasrin",
      title: "Board Member",
      image: "/assets/images/members/PROF BEGUM NASRIN.jpg"
    },
    {
      name: "Lt Col SM Nazrul Islam",
      title: "Board Member",
      image: "/assets/images/members/LT COL SM NAZRUL ISLAM.jpg"
    },
    {
      name: "Mahfuzur R Chowdhury",
      title: "Board Member",
      image: "/assets/images/members/MAHFUZUR R CHOWDHURY.jpg"
    },
    {
      name: "Ipsha Nazia Adiba",
      title: "Board Member",
      image: "/assets/images/members/IPSHA NAZIA ADIBA.jpg"
    },
    {
      name: "Dr A.R.M Momtajuddin",
      title: "Board Member",
      image: "/assets/images/members/ARM MOMTAJUDDIN.jpg"
    },
    {
      name: "Prof Dr Md Kamruzzaman",
      title: "Board Member",
      image: "/assets/images/members/PROF MD KAMRUZZAMAN.jpg"
    },
    {
      name: "Dr AFM Zohurul Haque",
      title: "Board Member",
      image: "/assets/images/members/DR AFM ZOHURUL HAQUE.jpg"
    },
    {
      name: "Dr Sohel Akhter",
      title: "Board Member",
      image: "/assets/images/members/DR SOHEL AKHTER.jpg"
    },
    {
      name: "Maj Gen Md Mahbubur Rahman",
      title: "Board Member",
      image: "/assets/images/members/MG MD MAHBUBUR RAHMAN.jpg"
    },
    {
      name: "Md Selim Reza",
      title: "Board Member",
      image: "/assets/images/members/MD SELIM REZA.jpg"
    },
    {
      name: "Brig Gen Md Delwar Hussain",
      title: "Board Member",
      image: "/assets/images/members/BG MD DELWAR HOSSAIN.jpg"
    },
    {
      name: "Col Md Mostafizur Rahman",
      title: "Board Member",
      image: "/assets/images/members/COL MDMOSTAFIZUR RAHMAN.jpg"
    },
    {
      name: "Lt Col Maksuda Begum",
      title: "Board Member",
      image: "/assets/images/members/LT COL MAKSUDA BEGUM.jpg"
    },
    {
      name: "Dr Shahana Begum",
      title: "Board Member",
      image: "/assets/images/members/DR. SHAHANA BEGUM.jpg"
    },
    {
      name: "Engr Selima Nargis",
      title: "Board Member",
      image: "/assets/images/members/ENGR. SELIMA NARGIS.jpg"
    },
    {
      name: "Prof Md Amanullah",
      title: "Board Member",
      image: "/assets/images/members/PROF MD AMANULLAH.jpg"
    },
    {
      name: "Afrin Sultana",
      title: "Board Member",
      image: "/assets/images/members/AFRIN SULTANA.jpg"
    },
    {
      name: "Lt Col Md Billal Hossain",
      title: "Board Member",
      image: "/assets/images/members/LT COL MD BILLAL HOSSAIN.jpg"
    },
    {
      name: "Md Mafrul Haque",
      title: "Board Member",
      image: "/assets/images/members/MD MAFRUL HAQUE.jpg"
    },
    {
      name: "Md Zakir H Khan",
      title: "Board Member",
      image: "/assets/images/members/MD ZAKIR H KHAN.jpg"
    },
    {
      name: "Mosammat Halima Begum",
      title: "Board Member",
      image: "/assets/images/members/MOSAMMAT HALIMA BEGUM.jpg"
    },
    {
      name: "Most Sayeeda Akhter",
      title: "Board Member",
      image: "/assets/images/members/MOST. SAYEEDA AKHTER.jpg"
    },
    {
      name: "Dr Mosammat Dipa",
      title: "Board Member",
      image: "/assets/images/members/DR MOSAMMAT DIPA.jpg"
    },
    {
      name: "Fihor Esrar Eham",
      title: "Board Member",
      image: "/assets/images/members/FIHOR ESRAR EHAM.jpg"
    }
  ];

  const values = [
    {
      icon: Shield,
      title: "Military Precision",
      description: "We bring the discipline and attention to detail from military service to every project we undertake."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "Uncompromising quality in every aspect of our work, from design to delivery."
    },
    {
      icon: Users,
      title: "Trust",
      description: "Building long-term relationships with our clients based on transparency and reliability."
    },
    {
      icon: Target,
      title: "Innovation",
      description: "Embracing cutting-edge technology and modern design principles in all our developments."
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-48 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="absolute inset-0">
          <video
            autoPlay
            loop
            muted
            playsInline
            preload="metadata"
            className="w-full h-full object-cover"
          >
            <source src="/assets/images/flowers.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div className="absolute inset-0 bg-black/40"></div>
        </div>
        {/* Title and Subtitle moved here */}
      </section>

      {/* Title and Subtitle after Hero Section */}
      <section className="bg-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-black mb-6">
            About <span className="text-green-800">Soldiers Builders</span>
          </h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Military precision meets luxury living. We build your dreams.
          </p>
        </div>
      </section>

      {/* Who We Are */}
      <section id="who-we-are" className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Who We Are
              </h2>
              <div className="space-y-6 text-gray-300 leading-relaxed">
                <p>
                  At Soldiers Builders, we build your dreams. Synonymous with
                  refined living, architectural excellence, and uncompromising quality, Soldiers Builders
                  stands as a testament to the perfect fusion of military discipline and civilian innovation.
                  With a foundation built on trust, integrity, innovation, and meticulous craftsmanship, Soldiers Builders is a symbol of
                  excellence in the real estate industry.
                </p>
                <p>
                  Our name represents our discipline, our dedication to detail, and our relentless pursuit 
                  of excellence — values reminiscent of elite forces, now translated into the art of construction.
                </p>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <img
                src="https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Luxury Building"
                className="w-full h-96 object-cover rounded-xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-yellow-400/20 to-transparent rounded-xl"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section id="mission-vision" className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex items-center mb-6">
                <div className="bg-yellow-400 p-3 rounded-lg mr-4">
                  <Target className="h-8 w-8 text-gray-900" />
                </div>
                <h3 className="text-3xl font-bold text-white">Our Mission</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                To deliver exceptional real estate experiences by combining visionary design, 
                high-end construction standards, and personalized service—ensuring every property 
                is not only a home but a statement. We are committed to exceeding expectations, 
                transforming dreams into reality, and creating spaces that resonate with elegance, 
                functionality, and enduring value.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex items-center mb-6">
                <div className="bg-yellow-400 p-3 rounded-lg mr-4">
                  <Eye className="h-8 w-8 text-gray-900" />
                </div>
                <h3 className="text-3xl font-bold text-white">Our Vision</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Shaping Tomorrow.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our Values
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              The principles that guide every decision and shape every project
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="bg-gray-800 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-300"
              >
                <div className="bg-yellow-400 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <value.icon className="h-8 w-8 text-gray-900" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{value.title}</h3>
                <p className="text-gray-400">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Message from MD & Chairman */}
      <section id="message" className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Message from MD & Chairman
            </h2>
          </motion.div>

          <div className="space-y-16">
            {/* Chairman's Message */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex flex-col md:flex-row items-start gap-8">
                <div className="flex-shrink-0">
                  <OptimizedImage
                    src="/assets/images/members/MAJ MD TOUFIKUL MUNEEM.jpg"
                    alt="Major Toufikul Muneem"
                    className="w-32 h-32 rounded-full"
                    width={128}
                    height={128}
                    quality={90} // Higher quality for leadership portraits
                    priority={true}
                  />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2">Chairman's Message</h3>
                  <h4 className="text-lg text-yellow-400 mb-4">Major Toufikul Muneem</h4>
                  <p className="text-gray-300 leading-relaxed">
                    "At Soldiers Builders, we do more than shape skylines, we shape futures. As someone who
                    has dedicated their life to serving the nation, I understand the importance of creating
                    spaces that not only provide shelter but also foster community, build legacy, and a sense of belonging. At Soldiers Builders, luxury is not only in how a space
                    looks, but in how it makes you feel."
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Managing Director's Message */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex flex-col md:flex-row items-start gap-8">
                <div className="flex-shrink-0 md:order-2">
                  <OptimizedImage
                    src="/assets/images/members/BRIG GEN MD ZOBAIDUR RAHMAN.jpg"
                    alt="Brigadier General Md Zobaidur Rahman"
                    className="w-32 h-32 rounded-full"
                    width={128}
                    height={128}
                    quality={90} // Higher quality for leadership portraits
                    priority={true}
                  />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2">Managing Director's Message</h3>
                  <h4 className="text-lg text-yellow-400 mb-4">Brigadier General Md Zobaidur Rahman</h4>
                  <p className="text-gray-300 leading-relaxed">
                    At Soldiers Builders, we bring a unique perspective - shaped by my years in uniform, where
                    precision, discipline, and attention to detail were not just values, but necessities.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    We serve individuals, families, and businesses who want more than a property. They want
                    confidence in the ground beneath their feet. They want a team that listens, understands,
                    and delivers. We consider it an honor to be trusted with that responsibility.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    We do not simply develop land - we cultivate futures. We understand that every family,
                    every investor, and every community that turns to us is placing their hopes into our hands.
                    And we honor that trust with a devotion to excellence that runs deeper than blueprints and budgets.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    Here, luxury is not just in the finish - it is in the philosophy. In the way we treat people.
                    In the legacy we leave behind. In every structure we build, there is strength. In every detail,
                    a promise. And behind every project - a team that leads with purpose."
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Board of Directors Carousel */}
      <section id="board" className="py-20 bg-gray-900">
        <BoardMembersCarousel members={leadership} />
      </section>

      {/* Quote Section */}
      <section className="py-20 bg-green-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              "The best investment on Earth is earth."
            </h2>
            <p className="text-xl text-white">
              — Louis Glickman
            </p>
          </motion.div>
        </div>
      </section>

      {/* Words That Inspire Us */}
      <section className="py-16 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">
              Words That Inspire Us
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "The best investment on Earth is earth."
              </p>
              <p className="text-yellow-400 font-semibold">— Louis Glickman</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "Your home should tell the story of who you are"
              </p>
              <p className="text-yellow-400 font-semibold">— Nate Berkus</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "To be happy at home is the ultimate result of all ambition."
              </p>
              <p className="text-yellow-400 font-semibold">— Samuel Johnson</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "He is the happiest, be he king or peasant, who finds peace in his home."
              </p>
              <p className="text-yellow-400 font-semibold">— Johann Wolfgang von Goethe</p>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-8 text-center"
          >
            <div className="bg-gray-900 p-6 rounded-xl max-w-4xl mx-auto">
              <p className="text-lg text-gray-300 italic mb-4">
                "Where we love is home; home that our feet may leave, but not our hearts."
              </p>
              <p className="text-yellow-400 font-semibold">— Oliver Wendell Holmes Sr.</p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA/Banner Section */}
      <section className="py-20 bg-green-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white">
            Ready to Start Your Project?
          </h2>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Let's discuss how we can bring your vision to life with our comprehensive services.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              Get Free Consultation
            </button>
            <button className="bg-white text-green-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200">
              View Our Portfolio
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;