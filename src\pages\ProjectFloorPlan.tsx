import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, FileText, ZoomIn, X, Download } from 'lucide-react';

const ProjectFloorPlan = () => {
  const { projectId } = useParams();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Project mapping for folder names
  const projectFolderMap: { [key: string]: string } = {
    'haruns-nest': 'Harun Nest',
    'mollicks-dream': "Molli<PERSON>'s Dream",
    'tilottoma': 'SAL TILOTTOMA',
    'mehnaz': 'Mehnaz',
    'habibs-utopia': "Habib's Utopia",
    'bondhu-bilash': 'Bondhu Bilash',
    'chandrima-bilash': 'Chondrima Bilash',
    'kashful': 'Kashful'
  };

  // Project names for display
  const projectNames: { [key: string]: string } = {
    'haruns-nest': "<PERSON><PERSON>'s Nest",
    'mollicks-dream': "<PERSON><PERSON><PERSON>'s Dream",
    'tilottoma': 'SAL Tilottoma',
    'mehnaz': 'Mehnaz',
    'habibs-utopia': "Ha<PERSON>b's Utopia",
    'bondhu-bilash': 'Bondhu Bilash',
    'chandrima-bilash': 'Chandrima Bilash',
    'kashful': 'Kashful'
  };

  // Floor plan images for each project
  const floorPlanImages: { [key: string]: string[] } = {
    'haruns-nest': [
      '/assets/images/interior ,floorplan, brochure/Harun Nest/floor plan/1.png',
      '/assets/images/interior ,floorplan, brochure/Harun Nest/floor plan/2.png',
      '/assets/images/interior ,floorplan, brochure/Harun Nest/floor plan/3.png',
      '/assets/images/interior ,floorplan, brochure/Harun Nest/floor plan/4.png'
    ],
    'mollicks-dream': [
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/Floor Plan/Picture1.png',
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/Floor Plan/Picture2.png',
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/Floor Plan/Picture3.png',
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/Floor Plan/Picture4.png',
      '/assets/images/interior ,floorplan, brochure/Mollick\'s Dream/Floor Plan/Picture5.png'
    ],
    'tilottoma': [
      '/assets/images/interior ,floorplan, brochure/SAL TILOTTOMA/Floor plan/1.png',
      '/assets/images/interior ,floorplan, brochure/SAL TILOTTOMA/Floor plan/2.png'
    ],
    'mehnaz': [
      '/assets/images/interior ,floorplan, brochure/Mehnaz/floor plan/Screenshot 2025-07-28 171522.png'
    ],
    'habibs-utopia': [
      '/assets/images/interior ,floorplan, brochure/Habib\'s Utopia/floor plan/1.png',
      '/assets/images/interior ,floorplan, brochure/Habib\'s Utopia/floor plan/2.png',
      '/assets/images/interior ,floorplan, brochure/Habib\'s Utopia/floor plan/3.png'
    ],
    'bondhu-bilash': [
      '/assets/images/interior ,floorplan, brochure/Bondhu Bilash/floorplan/1.png',
      '/assets/images/interior ,floorplan, brochure/Bondhu Bilash/floorplan/2.png',
      '/assets/images/interior ,floorplan, brochure/Bondhu Bilash/floorplan/3.png'
    ],
    'chandrima-bilash': [
      '/assets/images/interior ,floorplan, brochure/Chondrima Bilash/floorplan/1.png',
      '/assets/images/interior ,floorplan, brochure/Chondrima Bilash/floorplan/2.png',
      '/assets/images/interior ,floorplan, brochure/Chondrima Bilash/floorplan/3.png'
    ],
    'kashful': [
      '/assets/images/interior ,floorplan, brochure/Kashful/floorplan/1.png',
      '/assets/images/interior ,floorplan, brochure/Kashful/floorplan/2.png',
      '/assets/images/interior ,floorplan, brochure/Kashful/floorplan/3.png'
    ]
  };

  const projectName = projectNames[projectId as string] || 'Project';
  const images = floorPlanImages[projectId as string] || [];

  const handleDownload = (imageUrl: string, index: number) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${projectName}_FloorPlan_${index + 1}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Link 
              to={`/projects/${projectId}`}
              className="inline-flex items-center text-yellow-400 hover:text-yellow-300 mb-6 transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Project Details
            </Link>
            <div className="flex items-center mb-6">
              <FileText className="h-8 w-8 text-yellow-400 mr-4" />
              <h1 className="text-4xl md:text-6xl font-bold text-white">
                Floor Plans
              </h1>
            </div>
            <h2 className="text-2xl md:text-3xl text-gray-300 mb-4">
              {projectName}
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl">
              Detailed architectural layouts and floor plans showing the thoughtful design and 
              optimal space utilization of each unit in this premium residential project.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Floor Plan Gallery */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {images.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {images.map((image, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="relative group"
                >
                  <div className="relative overflow-hidden rounded-xl bg-gray-800 cursor-pointer"
                       onClick={() => setSelectedImage(image)}>
                    <img
                      src={image}
                      alt={`${projectName} Floor Plan ${index + 1}`}
                      className="w-full h-96 object-contain bg-white transition-transform duration-300 group-hover:scale-105"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <ZoomIn className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        Floor Plan {index + 1}
                      </h3>
                      <p className="text-gray-400 text-sm">
                        Click to view full size
                      </p>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownload(image, index);
                      }}
                      className="flex items-center space-x-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                    >
                      <Download className="h-4 w-4" />
                      <span>Download</span>
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <FileText className="h-16 w-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">
                Floor Plans Coming Soon
              </h3>
              <p className="text-gray-500">
                Detailed floor plans for {projectName} will be available soon.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Image Modal */}
      {selectedImage && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-6xl max-h-full">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
            >
              <X className="h-8 w-8" />
            </button>
            <img
              src={selectedImage}
              alt="Floor Plan Detail"
              className="max-w-full max-h-full object-contain rounded-lg bg-white"
            />
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ProjectFloorPlan;
